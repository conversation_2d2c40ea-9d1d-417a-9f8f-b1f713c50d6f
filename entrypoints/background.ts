import { defineBackground } from 'wxt/utils/define-background';

export default defineBackground(() => {
  // 简化版本用于测试Service Worker是否能正常启动
  console.log('🚀 Service Worker启动测试');

  // 消息监听器
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 收到消息:', request);

    if (request.type === 'ping') {
      console.log('🏓 响应ping');
      sendResponse({ success: true, message: 'pong' });
      return true;
    }

    // API迁移消息处理
    if (request.type === 'api-migration') {
      console.log('🎯 处理API迁移消息...');
      handleApiMigrationMessage(request, sender, sendResponse).catch(error => {
        console.error('处理API迁移消息异常:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true;
    }

    return false;
  });

  // 安装事件
  browser.runtime.onInstalled.addListener(() => {
    console.log('✅ Service Worker已安装');
  });

  // 启动事件
  browser.runtime.onStartup.addListener(() => {
    console.log('🔄 Service Worker已启动');
  });

  console.log('✅ Service Worker初始化完成');

  // 初始化API迁移功能
  initApiMigration().then(() => {
    console.log('✅ API迁移后台功能初始化完成');
  }).catch((error: any) => {
    console.error('❌ API迁移后台功能初始化失败:', error);
  });
});

// API迁移功能变量
let apiMigrationRules: any[] = [];
let isIntercepting = false;

// 初始化API迁移功能
async function initApiMigration() {
  console.log('🔧 API迁移验证工具后台功能已初始化');
  console.log('📋 开始加载API迁移规则...');
  await loadApiMigrationRules();
  console.log('✅ API迁移规则加载完成');
}

// 加载API迁移规则
async function loadApiMigrationRules() {
  try {
    const result = await browser.storage.local.get(['apiMigrationRules']);
    apiMigrationRules = result.apiMigrationRules || [];
    console.log('📋 已加载', apiMigrationRules.length, '条API迁移规则');
  } catch (error) {
    console.error('加载API迁移规则失败:', error);
    apiMigrationRules = [];
  }
}

// 保存API迁移规则
async function saveApiMigrationRules() {
  try {
    await browser.storage.local.set({ apiMigrationRules });
    console.log('💾 API迁移规则已保存');
  } catch (error) {
    console.error('保存API迁移规则失败:', error);
  }
}

// API迁移功能
async function handleApiMigrationMessage(request: any, sender: any, sendResponse: any) {
  console.log('📨 background收到API迁移消息:', request);

  try {
    let response;

    switch (request.action) {
      case 'start-interceptor':
        await startApiInterceptor();
        response = { success: true };
        break;

      case 'stop-interceptor':
        await stopApiInterceptor();
        response = { success: true };
        break;

      case 'get-status':
        console.log('📊 返回拦截器状态:', isIntercepting);
        response = { success: true, isIntercepting };
        break;

      case 'update-rules':
        apiMigrationRules = request.rules;
        await saveApiMigrationRules();
        response = { success: true };
        break;

      case 'get-rules':
        console.log('📋 返回规则数据:', apiMigrationRules.length, '条规则');
        response = { success: true, rules: apiMigrationRules };
        break;

      default:
        response = { success: false, error: '未知动作' };
    }

    sendResponse(response);
  } catch (error: any) {
    console.error('处理API迁移消息失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 网络拦截功能
async function startApiInterceptor() {
  if (isIntercepting) {
    console.log('API迁移拦截器已在运行');
    return;
  }

  try {
    console.log('🔄 启动API迁移拦截器');

    // 添加请求拦截器 - 移除了blocking参数
    browser.webRequest.onBeforeRequest.addListener(
      handleBeforeRequest,
      { urls: ['<all_urls>'] }
    );

    isIntercepting = true;
    console.log('✅ API迁移拦截器已启动');
  } catch (error) {
    console.error('启动API迁移拦截器失败:', error);
    throw error;
  }
}

async function stopApiInterceptor() {
  if (!isIntercepting) {
    console.log('API迁移拦截器未在运行');
    return;
  }

  try {
    console.log('⏹️ 停止API迁移拦截器');

    browser.webRequest.onBeforeRequest.removeListener(handleBeforeRequest);

    isIntercepting = false;
    console.log('✅ API迁移拦截器已停止');
  } catch (error) {
    console.error('停止API迁移拦截器失败:', error);
    throw error;
  }
}

// 处理请求拦截
function handleBeforeRequest(details: any): undefined {
  // 简单的调试日志
  console.log('🌐 拦截到请求:', details.url, '类型:', details.type);

  // 只处理主要的API请求
  if (details.type !== 'main_frame' && details.type !== 'xmlhttprequest' && details.type !== 'fetch') {
    return undefined;
  }

  console.log('🔍 处理API请求:', details.url);

  const rule = findMatchingRule(details.url, details.method);
  if (!rule) {
    console.log('❌ 未找到匹配规则');
    return undefined;
  }

  console.log('✅ 找到匹配规则:', rule.name);

  try {
    // 检查规则模式
    if (rule.mode === 'redirect') {
      // 重定向模式（在Manifest V3中只能记录）
      const newUrl = transformUrl(details.url, rule);
      console.log('� 重定向模式记录:', { original: details.url, new: newUrl });
    } else if (rule.mode === 'parallel') {
      // 并行对比模式
      console.log('🔄 启动并行对比模式');
      performParallelComparison(details, rule).catch(error => {
        console.error('并行对比失败:', error);
      });
    } else {
      // 默认记录模式
      console.log('📊 记录API请求:', {
        original: details.url,
        rule: rule.name,
        method: details.method
      });
    }

  } catch (error) {
    console.error('处理API请求失败:', error);
  }

  return undefined;
}

// 查找匹配的规则
function findMatchingRule(url: string, method: string) {
  const enabledRules = apiMigrationRules.filter(rule => rule.enabled)
    .sort((a, b) => b.priority - a.priority);

  for (const rule of enabledRules) {
    if (isRuleMatching(rule, url, method)) {
      return rule;
    }
  }

  return null;
}

// 检查规则是否匹配
function isRuleMatching(rule: any, url: string, method: string) {
  const { conditions } = rule;

  // 检查URL匹配
  if (!isUrlMatching(conditions.urlPattern, conditions.urlMatchType, url)) {
    return false;
  }

  // 检查方法匹配
  if (conditions.methods && conditions.methods.length > 0 &&
      !conditions.methods.includes(method.toUpperCase())) {
    return false;
  }

  return true;
}

// URL匹配检查
function isUrlMatching(pattern: string, matchType: string, url: string) {
  switch (matchType) {
    case 'exact':
      return url === pattern;
    case 'contains':
      return url.includes(pattern);
    case 'startsWith':
      return url.startsWith(pattern);
    case 'endsWith':
      return url.endsWith(pattern);
    case 'regex':
      try {
        return new RegExp(pattern).test(url);
      } catch {
        return false;
      }
    default:
      return url.includes(pattern);
  }
}

// URL转换
function transformUrl(originalUrl: string, rule: any) {
  const { transformation } = rule;
  let newUrl = transformation.newUrl;

  // 处理参数映射
  if (transformation.paramMapping) {
    try {
      const originalUrlObj = new URL(originalUrl);
      const newUrlObj = new URL(newUrl);

      // 映射参数
      for (const [oldParam, newParam] of Object.entries(transformation.paramMapping)) {
        const value = originalUrlObj.searchParams.get(oldParam);
        if (value && newParam) {
          newUrlObj.searchParams.set(newParam as string, value);
        }
      }

      // 保留原始参数
      if (transformation.preserveOriginalParams) {
        originalUrlObj.searchParams.forEach((value, key) => {
          if (!transformation.paramMapping[key] && !newUrlObj.searchParams.has(key)) {
            newUrlObj.searchParams.set(key, value);
          }
        });
      }

      newUrl = newUrlObj.toString();
    } catch (error) {
      console.warn('URL转换失败:', error);
    }
  }

  return newUrl;
}

// 并行对比功能
async function performParallelComparison(details: any, rule: any) {
  try {
    console.log('🔄 开始并行对比:', details.url);

    console.log('🔧 开始URL转换...');
    const newUrl = transformUrl(details.url, rule);
    console.log('🎯 对比URL:', { original: details.url, new: newUrl });
    console.log('✅ URL转换完成，准备创建报告...');

    // 创建对比报告
    console.log('📝 创建对比报告对象...');
    const report = {
      id: 'report_' + Date.now(),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: details.url,
        method: details.method,
        newUrl: newUrl
      },
      status: 'completed',
      diff: {
        changeCount: 0, // 在Manifest V3中我们无法实际对比，设为0
        summary: '网络拦截记录（Manifest V3限制）'
      },
      severity: 'info'
    };
    console.log('✅ 对比报告对象创建完成:', report);

    // 保存对比报告到storage
    try {
      console.log('💾 保存对比报告到storage...');

      // 获取现有的报告列表
      const result = await browser.storage.local.get('apiMigrationReports');
      const reports = result.apiMigrationReports || [];

      // 添加新报告到列表开头
      reports.unshift(report);

      // 限制报告数量（保留最新的100个）
      if (reports.length > 100) {
        reports.length = 100;
      }

      // 保存到storage
      await browser.storage.local.set({ apiMigrationReports: reports });

      console.log('✅ 对比报告已保存到storage:', report.id);
    } catch (error) {
      console.error('❌ 保存报告到storage失败:', error);
    }

    console.log('✅ 并行对比报告已发送:', report.id);
  } catch (error) {
    console.error('并行对比失败:', error);
  }
}

